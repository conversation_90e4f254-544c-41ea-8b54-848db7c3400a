/*******************************************************************************
 * 激光雕刻机控制系统 - 单片机主程序
 * 目标芯片: STC15W201S
 * 开发环境: Keil uVision 5
 * 版本: v1.0.0
 * 日期: 2025-07-20
 ******************************************************************************/

#include "STC15W201S.h"
#include "uart.h"
#include "stepper.h"
#include "laser.h"
#include "protocol.h"
#include "system.h"

/*******************************************************************************
 * 全局变量定义
 ******************************************************************************/
volatile uint8_t system_state = SYS_IDLE;    // 系统状态
volatile uint16_t current_x = 0;             // 当前X坐标
volatile uint16_t current_y = 0;             // 当前Y坐标
volatile uint8_t laser_power = 0;            // 激光功率
volatile uint8_t move_speed = 50;            // 移动速度

/*******************************************************************************
 * 主函数
 ******************************************************************************/
void main(void)
{
    // 系统初始化
    System_Init();
    UART_Init();
    Stepper_Init();
    Laser_Init();
    Protocol_Init();
    
    // 使能全局中断
    EA = 1;
    
    // 发送启动信息
    UART_SendString("Laser Engraver Ready\r\n");
    
    while(1)
    {
        // 处理串口接收的命令
        Protocol_ProcessCommand();
        
        // 执行步进电机运动
        Stepper_Process();
        
        // 更新激光状态
        Laser_Process();
        
        // 系统状态监控
        System_Monitor();
        
        // 看门狗喂狗
        System_WatchdogFeed();
    }
}

/*******************************************************************************
 * 定时器0中断服务程序 - 用于步进电机脉冲生成
 ******************************************************************************/
void Timer0_ISR(void) interrupt(1)
{
    TH0 = 0xFC;  // 重装载定时器值 (1ms @ 12MHz)
    TL0 = 0x18;
    
    // 步进电机脉冲生成
    Stepper_TimerISR();
    
    // 激光PWM更新
    Laser_TimerISR();
}

/*******************************************************************************
 * 串口中断服务程序
 ******************************************************************************/
void UART_ISR(void) interrupt(4)
{
    if(RI)  // 接收中断
    {
        RI = 0;
        UART_ReceiveISR();
    }
    
    if(TI)  // 发送中断
    {
        TI = 0;
        UART_TransmitISR();
    }
}
