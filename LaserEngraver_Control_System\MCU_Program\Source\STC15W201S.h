/*******************************************************************************
 * STC15W201S 芯片寄存器定义
 * 兼容 SDCC 和 Keil C51 编译器
 ******************************************************************************/

#ifndef __STC15W201S_H__
#define __STC15W201S_H__

/*******************************************************************************
 * 标准数据类型定义
 ******************************************************************************/
typedef unsigned char  uint8_t;
typedef unsigned int   uint16_t;
typedef unsigned long  uint32_t;
typedef signed char    int8_t;
typedef signed int     int16_t;
typedef signed long    int32_t;

/*******************************************************************************
 * 编译器兼容性定义
 ******************************************************************************/
#ifdef SDCC
    #include <mcs51/8051.h>
    // SDCC 使用标准的8051头文件
    #define interrupt(x) __interrupt(x)
    #define using(x) __using(x)
#else
    // Keil C51 编译器
    /*******************************************************************************
     * SFR 寄存器定义
     ******************************************************************************/
    sfr P0      = 0x80;
    sfr P1      = 0x90;
    sfr P2      = 0xA0;
    sfr P3      = 0xB0;
    sfr PSW     = 0xD0;
    sfr ACC     = 0xE0;
    sfr B       = 0xF0;
    sfr SP      = 0x81;
    sfr DPL     = 0x82;
    sfr DPH     = 0x83;
    sfr PCON    = 0x87;
    sfr TCON    = 0x88;
    sfr TMOD    = 0x89;
    sfr TL0     = 0x8A;
    sfr TL1     = 0x8B;
    sfr TH0     = 0x8C;
    sfr TH1     = 0x8D;
    sfr IE      = 0xA8;
    sfr IP      = 0xB8;
    sfr SCON    = 0x98;
    sfr SBUF    = 0x99;

    /*******************************************************************************
     * 位定义
     ******************************************************************************/
    // PSW 寄存器位定义
    sbit CY     = PSW^7;
    sbit AC     = PSW^6;
    sbit F0     = PSW^5;
    sbit RS1    = PSW^4;
    sbit RS0    = PSW^3;
    sbit OV     = PSW^2;
    sbit P      = PSW^0;

    // TCON 寄存器位定义
    sbit TF1    = TCON^7;
    sbit TR1    = TCON^6;
    sbit TF0    = TCON^5;
    sbit TR0    = TCON^4;
    sbit IE1    = TCON^3;
    sbit IT1    = TCON^2;
    sbit IE0    = TCON^1;
    sbit IT0    = TCON^0;

    // IE 寄存器位定义
    sbit EA     = IE^7;
    sbit ES     = IE^4;
    sbit ET1    = IE^3;
    sbit EX1    = IE^2;
    sbit ET0    = IE^1;
    sbit EX0    = IE^0;

    // SCON 寄存器位定义
    sbit SM0    = SCON^7;
    sbit SM1    = SCON^6;
    sbit SM2    = SCON^5;
    sbit REN    = SCON^4;
    sbit TB8    = SCON^3;
    sbit RB8    = SCON^2;
    sbit TI     = SCON^1;
    sbit RI     = SCON^0;

    /*******************************************************************************
     * 引脚定义 - 激光雕刻机专用
     ******************************************************************************/
    // 串口引脚 (P3.0/P3.1)
    sbit RXD    = P3^0;  // 引脚9  - 串口接收
    sbit TXD    = P3^1;  // 引脚10 - 串口发送

    // X轴步进电机引脚 (P1.0-P1.3)
    sbit X_A    = P1^0;  // 引脚15 - X轴A相
    sbit X_B    = P1^1;  // 引脚16 - X轴B相
    sbit X_C    = P1^2;  // 引脚1  - X轴C相
    sbit X_D    = P1^3;  // 引脚2  - X轴D相

    // Y轴步进电机引脚 (P3.2,P3.3,P3.6,P3.7)
    sbit Y_A    = P3^2;  // 引脚11 - Y轴A相
    sbit Y_B    = P3^3;  // 引脚12 - Y轴B相
    sbit Y_C    = P3^6;  // 引脚13 - Y轴C相
    sbit Y_D    = P3^7;  // 引脚14 - Y轴D相

    // 激光控制引脚 (可根据实际硬件调整)
    sbit LASER_PWM = P2^0;  // 激光PWM控制
    sbit LASER_EN  = P2^1;  // 激光使能

#endif // SDCC vs Keil

/*******************************************************************************
 * 系统配置参数
 ******************************************************************************/
#define FOSC        12000000L   // 系统时钟频率 12MHz
#define BAUD        9600        // 串口波特率
#define TIMER0_RELOAD_1MS  0xFC18  // 定时器0重装载值(1ms)

/*******************************************************************************
 * 系统状态定义
 ******************************************************************************/
#define SYS_IDLE    0   // 空闲状态
#define SYS_RUNNING 1   // 运行状态
#define SYS_PAUSE   2   // 暂停状态
#define SYS_ERROR   3   // 错误状态

/*******************************************************************************
 * 步进电机参数
 ******************************************************************************/
#define STEPS_PER_REV   200     // 每转步数
#define MICROSTEPS      8       // 微步数
#define MAX_SPEED       100     // 最大速度百分比
#define MIN_SPEED       1       // 最小速度百分比

/*******************************************************************************
 * 激光参数
 ******************************************************************************/
#define MAX_LASER_POWER 100     // 最大激光功率百分比
#define PWM_RESOLUTION  100     // PWM分辨率

#endif // __STC15W201S_H__
